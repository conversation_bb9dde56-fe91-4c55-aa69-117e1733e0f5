import React from 'react';
import { formatPeso } from './utils/currency';

const TestPage = () => {
  // Test data to verify formatPeso function works
  const testData = {
    price1: 1250,
    price2: 750,
    price3: 56250
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
        <h1 className="text-3xl font-bold text-green-600 mb-4">✅ React is Working!</h1>
        <p className="text-gray-600 mb-4">If you can see this page, your basic setup is correct.</p>
        <div className="space-y-2 text-sm text-gray-500 mb-6">
          <p>✅ Vite is running</p>
          <p>✅ React is loaded</p>
          <p>✅ Tailwind CSS is working</p>
        </div>

        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
          <h3 className="text-lg font-medium text-blue-800 mb-2">💰 Currency Test</h3>
          <div className="text-blue-700 space-y-1 text-sm">
            <p>Price 1: {formatPeso(testData.price1)}</p>
            <p>Price 2: {formatPeso(testData.price2)}</p>
            <p>Price 3: {formatPeso(testData.price3)}</p>
          </div>
        </div>

        <a
          href="/"
          className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          Go to Homepage
        </a>
      </div>
    </div>
  );
};

export default TestPage;

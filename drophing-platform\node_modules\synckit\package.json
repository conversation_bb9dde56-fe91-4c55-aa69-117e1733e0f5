{"name": "synckit", "version": "0.11.8", "type": "module", "description": "Perform async work synchronously in Node.js using `worker_threads` with first-class TypeScript support.", "repository": "https://github.com/un-ts/synckit.git", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "funding": "https://opencollective.com/synckit", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "./lib/index.cjs", "types": "./lib/index.d.cts", "module": "./lib/index.js", "exports": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "require": {"types": "./lib/index.d.cts", "default": "./lib/index.cjs"}}, "files": ["index.d.cts", "lib", "!**/*.tsbuildinfo"], "keywords": ["deas<PERSON>", "make-synchronized", "make-synchronous", "sync", "sync-exec", "sync-rpc", "sync-threads", "synchronize", "synckit"], "dependencies": {"@pkgr/core": "^0.2.4"}}
import React from 'react';
import DashboardLayout from '../../components/layout/DashboardLayout';
import { formatPeso } from '../../utils/currency';
import {
  CurrencyDollarIcon,
  ClipboardDocumentListIcon,
  CubeIcon,
  ChartBarIcon,
  HomeIcon,
  PlusIcon,
  ShoppingBagIcon,
  ArchiveBoxIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  EyeIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';

const SellerDashboard = () => {
  // Mock data - in real app, this would come from API
  const stats = {
    totalSales: 622500, // ₱622,500 (was $12,450)
    totalOrders: 156,
    activeProducts: 23,
    conversionRate: 3.2
  };

  const recentOrders = [
    { id: '#12345', customer: '<PERSON>', product: 'Custom T-Shirt', amount: 1250, status: 'Processing', date: '2024-01-15' },
    { id: '#12346', customer: '<PERSON>', product: 'Custom Mug', amount: 750, status: 'Shipped', date: '2024-01-14' },
    { id: '#12347', customer: '<PERSON>', product: 'Phone Case', amount: 1000, status: 'Delivered', date: '2024-01-13' },
  ];

  const topProducts = [
    { name: 'Custom T-Shirt', sales: 45, revenue: 56250 }, // ₱56,250 (was $1,125)
    { name: 'Custom Mug', sales: 32, revenue: 24000 }, // ₱24,000 (was $480)
    { name: 'Phone Case', sales: 28, revenue: 28000 }, // ₱28,000 (was $560)
  ];

  const navigation = [
    {
      name: 'Dashboard',
      href: '/seller/dashboard',
      icon: HomeIcon
    },
    {
      name: 'Product Creator',
      href: '/seller/products/create',
      icon: PlusIcon
    },
    {
      name: 'My Products',
      href: '/seller/products',
      icon: CubeIcon,
      badge: stats.activeProducts
    },
    {
      name: 'Orders',
      href: '/seller/orders',
      icon: ClipboardDocumentListIcon,
      badge: stats.totalOrders
    },
    {
      name: 'Analytics',
      href: '/seller/analytics',
      icon: ChartBarIcon
    },
    {
      name: 'Inventory',
      href: '/seller/inventory',
      icon: ArchiveBoxIcon
    },
    {
      name: 'Store Integrations',
      href: '/seller/integrations',
      icon: ShoppingBagIcon
    },
    {
      name: 'Support',
      href: '/seller/support',
      icon: ChatBubbleLeftRightIcon
    },
    {
      name: 'Settings',
      href: '/seller/settings',
      icon: Cog6ToothIcon
    }
  ];

  // Quick actions for the top bar
  const quickActions = (
    <>
      <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
        <PlusIcon className="w-4 h-4 mr-2" />
        Create Product
      </button>
      <button className="inline-flex items-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm">
        <EyeIcon className="w-4 h-4 mr-2" />
        View Store
      </button>
    </>
  );

  return (
    <DashboardLayout navigation={navigation} title="Dashboard" actions={quickActions}>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Sales Card */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm font-medium">Total Sales</p>
              <p className="text-3xl font-bold">{formatPeso(stats.totalSales)}</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                <span className="text-sm">+12.5% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-white/20 rounded-lg">
              <CurrencyDollarIcon className="w-8 h-8" />
            </div>
          </div>
        </div>

        {/* Total Orders Card */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm font-medium">Total Orders</p>
              <p className="text-3xl font-bold">{stats.totalOrders}</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                <span className="text-sm">+8.2% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-white/20 rounded-lg">
              <ClipboardDocumentListIcon className="w-8 h-8" />
            </div>
          </div>
        </div>

        {/* Active Products Card */}
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm font-medium">Active Products</p>
              <p className="text-3xl font-bold">{stats.activeProducts}</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                <span className="text-sm">+3 new this week</span>
              </div>
            </div>
            <div className="p-3 bg-white/20 rounded-lg">
              <CubeIcon className="w-8 h-8" />
            </div>
          </div>
        </div>

        {/* Conversion Rate Card */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm font-medium">Conversion Rate</p>
              <p className="text-3xl font-bold">{stats.conversionRate}%</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                <span className="text-sm">+0.5% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-white/20 rounded-lg">
              <ChartBarIcon className="w-8 h-8" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold text-gray-900">Recent Orders</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">View All</button>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <ClipboardDocumentListIcon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{order.id}</p>
                      <p className="text-sm text-gray-600">{order.customer} • {order.product}</p>
                      <p className="text-xs text-gray-500">{order.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900 text-lg">{formatPeso(order.amount)}</p>
                    <span className={`inline-flex px-3 py-1 text-xs font-bold rounded-full ${
                      order.status === 'Delivered' ? 'bg-green-100 text-green-800' :
                      order.status === 'Shipped' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {order.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold text-gray-900">Top Products</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">View All</button>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={product.name} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-white ${
                      index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                      index === 1 ? 'bg-gradient-to-r from-gray-400 to-gray-500' :
                      'bg-gradient-to-r from-orange-400 to-orange-500'
                    }`}>
                      #{index + 1}
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <CubeIcon className="w-6 h-6 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{product.name}</p>
                        <p className="text-sm text-gray-600">{product.sales} sales</p>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900 text-lg">{formatPeso(product.revenue)}</p>
                    <div className="flex items-center text-green-600 text-sm">
                      <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                      <span>+15%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white rounded-xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button className="group flex items-center p-6 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-200 transform hover:scale-105 hover:shadow-lg">
            <div className="p-3 bg-blue-600 rounded-xl mr-4 group-hover:bg-blue-700 transition-colors">
              <PlusIcon className="w-8 h-8 text-white" />
            </div>
            <div className="text-left">
              <p className="font-bold text-gray-900 text-lg">Create New Product</p>
              <p className="text-sm text-gray-600">Design and launch a new product</p>
            </div>
          </button>

          <button className="group flex items-center p-6 bg-gradient-to-r from-green-50 to-green-100 rounded-xl hover:from-green-100 hover:to-green-200 transition-all duration-200 transform hover:scale-105 hover:shadow-lg">
            <div className="p-3 bg-green-600 rounded-xl mr-4 group-hover:bg-green-700 transition-colors">
              <ChartBarIcon className="w-8 h-8 text-white" />
            </div>
            <div className="text-left">
              <p className="font-bold text-gray-900 text-lg">View Analytics</p>
              <p className="text-sm text-gray-600">Check your sales performance</p>
            </div>
          </button>

          <button className="group flex items-center p-6 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 transform hover:scale-105 hover:shadow-lg">
            <div className="p-3 bg-purple-600 rounded-xl mr-4 group-hover:bg-purple-700 transition-colors">
              <ShoppingBagIcon className="w-8 h-8 text-white" />
            </div>
            <div className="text-left">
              <p className="font-bold text-gray-900 text-lg">Connect Store</p>
              <p className="text-sm text-gray-600">Link your online store</p>
            </div>
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SellerDashboard;

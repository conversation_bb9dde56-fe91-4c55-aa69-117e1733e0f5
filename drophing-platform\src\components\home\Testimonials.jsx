import React from 'react';
import { formatPeso } from '../../utils/currency';

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'Online Seller',
      company: 'Pinoy Prints PH',
      quote: 'Grabe! Drophing totally changed my life! From zero income to ₱500k per month in just 8 months. Sobrang user-friendly ng platform, perfect para sa mga Pinoy entrepreneurs like me!',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&faces=entropy&facepad=3',
      rating: 5,
      revenue: formatPeso(500000) + '/month',
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Digital Entrepreneur',
      company: 'Tindahan ni <PERSON>',
      quote: 'Ang galing ng quality ng mga products! Customers ko super happy, and yung automated fulfillment saves me so much time. Best investment ever para sa business ko!',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&faces=entropy&facepad=3',
      rating: 5,
      revenue: formatPeso(750000) + '/month',
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Creative Designer',
      company: 'Artisan Manila',
      quote: 'From design hanggang delivery, lahat seamless! Yung mockup generator is fantastic, and my profit margins are mas better than ever. Highly recommended talaga!',
      image: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&faces=entropy&facepad=3',
      rating: 5,
      revenue: formatPeso(400000) + '/month',
    },
    {
      id: 4,
      name: 'Carlos Mendoza',
      role: 'OFW Entrepreneur',
      company: 'Balikbayan Prints',
      quote: 'Kahit nasa abroad ako, nakaka-manage ko pa rin yung business ko sa Pinas! Drophing made it possible para sa akin na mag-start ng business kahit malayo. Salamat talaga!',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&faces=entropy&facepad=3',
      rating: 5,
      revenue: formatPeso(300000) + '/month',
    },
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Mga Success Stories ng mga Kababayan
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Tingnan kung paano ang mga Filipino entrepreneurs tulad mo ay naging successful gamit ang Drophing
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-8 relative overflow-hidden"
            >
              {/* Background Decoration */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-10 translate-x-10 opacity-50"></div>

              {/* Quote Icon */}
              <div className="text-blue-600 mb-6">
                <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>

              {/* Quote */}
              <p className="text-gray-700 text-lg leading-relaxed mb-6 relative z-10">
                "{testimonial.quote}"
              </p>

              {/* Rating */}
              <div className="flex items-center mb-6">
                <div className="flex text-yellow-400">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <span className="ml-2 text-sm text-gray-600">5.0</span>
              </div>

              {/* Author Info */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-14 h-14 rounded-full object-cover mr-4 ring-4 ring-blue-100"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900 text-lg">{testimonial.name}</h4>
                    <p className="text-gray-600 text-sm">{testimonial.role}</p>
                    <p className="text-blue-600 text-sm font-medium">{testimonial.company}</p>
                  </div>
                </div>

                {/* Revenue Badge */}
                <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-bold">
                  {testimonial.revenue}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16 p-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white">
          <h3 className="text-3xl font-bold mb-4">
            Ready na ba kayong maging Success Story din?
          </h3>
          <p className="text-blue-100 text-lg mb-6 max-w-2xl mx-auto">
            Sumali sa libu-libong Filipino entrepreneurs na kumikita na ng malaki gamit ang Drophing
          </p>
          <button className="bg-white text-blue-600 font-bold py-4 px-8 rounded-full hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-xl">
            Simulan ang Inyong Journey Ngayon
          </button>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
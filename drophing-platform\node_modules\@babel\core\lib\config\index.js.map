{"version": 3, "names": ["_gensync", "data", "require", "_full", "_partial", "_item", "_rewriteStackTrace", "loadPartialConfigRunner", "gens<PERSON>", "loadPartialConfigImpl", "loadPartialConfigAsync", "args", "beginHiddenCallStack", "async", "loadPartialConfigSync", "sync", "loadPartialConfig", "opts", "callback", "undefined", "errback", "loadOptionsImpl", "_config$options", "config", "loadFullConfig", "options", "loadOptionsRunner", "loadOptionsAsync", "loadOptionsSync", "loadOptions", "createConfigItemRunner", "createConfigItemImpl", "createConfigItemAsync", "createConfigItemSync", "createConfigItem", "target"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["import gensync, { type Handler } from \"gensync\";\n\nexport type {\n  ResolvedConfig,\n  InputOptions,\n  PluginPasses,\n  Plugin,\n} from \"./full.ts\";\n\nimport type { InputOptions, PluginTarget } from \"./validation/options.ts\";\nexport type { ConfigAPI } from \"./helpers/config-api.ts\";\nimport type {\n  PluginAPI as basePluginAPI,\n  PresetAPI as basePresetAPI,\n} from \"./helpers/config-api.ts\";\nexport type { PluginObject } from \"./validation/plugins.ts\";\ntype PluginAPI = basePluginAPI & typeof import(\"..\");\ntype PresetAPI = basePresetAPI & typeof import(\"..\");\nexport type { PluginAPI, PresetAPI };\n// todo: may need to refine PresetObject to be a subset of ValidatedOptions\nexport type {\n  CallerMetadata,\n  ValidatedOptions as PresetObject,\n} from \"./validation/options.ts\";\n\nimport loadFullConfig, { type ResolvedConfig } from \"./full.ts\";\nimport {\n  type PartialConfig,\n  loadPartialConfig as loadPartialConfigImpl,\n} from \"./partial.ts\";\n\nexport { loadFullConfig as default };\nexport type { PartialConfig } from \"./partial.ts\";\n\nimport { createConfigItem as createConfigItemImpl } from \"./item.ts\";\nimport type { ConfigItem } from \"./item.ts\";\nexport type { ConfigItem };\n\nimport { beginHiddenCallStack } from \"../errors/rewrite-stack-trace.ts\";\n\nconst loadPartialConfigRunner = gensync(loadPartialConfigImpl);\nexport function loadPartialConfigAsync(\n  ...args: Parameters<typeof loadPartialConfigRunner.async>\n) {\n  return beginHiddenCallStack(loadPartialConfigRunner.async)(...args);\n}\nexport function loadPartialConfigSync(\n  ...args: Parameters<typeof loadPartialConfigRunner.sync>\n) {\n  return beginHiddenCallStack(loadPartialConfigRunner.sync)(...args);\n}\nexport function loadPartialConfig(\n  opts: Parameters<typeof loadPartialConfigImpl>[0],\n  callback?: (err: Error, val: PartialConfig | null) => void,\n) {\n  if (callback !== undefined) {\n    beginHiddenCallStack(loadPartialConfigRunner.errback)(opts, callback);\n  } else if (typeof opts === \"function\") {\n    beginHiddenCallStack(loadPartialConfigRunner.errback)(\n      undefined,\n      opts as (err: Error, val: PartialConfig | null) => void,\n    );\n  } else {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'loadPartialConfig' function expects a callback. If you need to call it synchronously, please use 'loadPartialConfigSync'.\",\n      );\n    } else {\n      return loadPartialConfigSync(opts);\n    }\n  }\n}\n\nfunction* loadOptionsImpl(opts: InputOptions): Handler<ResolvedConfig | null> {\n  const config = yield* loadFullConfig(opts);\n  // NOTE: We want to return \"null\" explicitly, while ?. alone returns undefined\n  return config?.options ?? null;\n}\nconst loadOptionsRunner = gensync(loadOptionsImpl);\nexport function loadOptionsAsync(\n  ...args: Parameters<typeof loadOptionsRunner.async>\n) {\n  return beginHiddenCallStack(loadOptionsRunner.async)(...args);\n}\nexport function loadOptionsSync(\n  ...args: Parameters<typeof loadOptionsRunner.sync>\n) {\n  return beginHiddenCallStack(loadOptionsRunner.sync)(...args);\n}\nexport function loadOptions(\n  opts: Parameters<typeof loadOptionsImpl>[0],\n  callback?: (err: Error, val: ResolvedConfig | null) => void,\n) {\n  if (callback !== undefined) {\n    beginHiddenCallStack(loadOptionsRunner.errback)(opts, callback);\n  } else if (typeof opts === \"function\") {\n    beginHiddenCallStack(loadOptionsRunner.errback)(\n      undefined,\n      opts as (err: Error, val: ResolvedConfig | null) => void,\n    );\n  } else {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'loadOptions' function expects a callback. If you need to call it synchronously, please use 'loadOptionsSync'.\",\n      );\n    } else {\n      return loadOptionsSync(opts);\n    }\n  }\n}\n\nconst createConfigItemRunner = gensync(createConfigItemImpl);\nexport function createConfigItemAsync(\n  ...args: Parameters<typeof createConfigItemRunner.async>\n) {\n  return beginHiddenCallStack(createConfigItemRunner.async)(...args);\n}\nexport function createConfigItemSync(\n  ...args: Parameters<typeof createConfigItemRunner.sync>\n) {\n  return beginHiddenCallStack(createConfigItemRunner.sync)(...args);\n}\nexport function createConfigItem(\n  target: PluginTarget,\n  options: Parameters<typeof createConfigItemImpl>[1],\n  callback?: (err: Error, val: ConfigItem<PluginAPI> | null) => void,\n) {\n  if (callback !== undefined) {\n    beginHiddenCallStack(createConfigItemRunner.errback)(\n      target,\n      options,\n      callback,\n    );\n  } else if (typeof options === \"function\") {\n    beginHiddenCallStack(createConfigItemRunner.errback)(\n      target,\n      undefined,\n      callback,\n    );\n  } else {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'createConfigItem' function expects a callback. If you need to call it synchronously, please use 'createConfigItemSync'.\",\n      );\n    } else {\n      return createConfigItemSync(target, options);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAyBA,IAAAE,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAQA,IAAAG,KAAA,GAAAH,OAAA;AAIA,IAAAI,kBAAA,GAAAJ,OAAA;AAEA,MAAMK,uBAAuB,GAAGC,SAAMA,CAAC,CAACC,0BAAqB,CAAC;AACvD,SAASC,sBAAsBA,CACpC,GAAGC,IAAsD,EACzD;EACA,OAAO,IAAAC,uCAAoB,EAACL,uBAAuB,CAACM,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AACrE;AACO,SAASG,qBAAqBA,CACnC,GAAGH,IAAqD,EACxD;EACA,OAAO,IAAAC,uCAAoB,EAACL,uBAAuB,CAACQ,IAAI,CAAC,CAAC,GAAGJ,IAAI,CAAC;AACpE;AACO,SAASK,iBAAiBA,CAC/BC,IAAiD,EACjDC,QAA0D,EAC1D;EACA,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IAC1B,IAAAP,uCAAoB,EAACL,uBAAuB,CAACa,OAAO,CAAC,CAACH,IAAI,EAAEC,QAAQ,CAAC;EACvE,CAAC,MAAM,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IACrC,IAAAL,uCAAoB,EAACL,uBAAuB,CAACa,OAAO,CAAC,CACnDD,SAAS,EACTF,IACF,CAAC;EACH,CAAC,MAAM;IAKE;MACL,OAAOH,qBAAqB,CAACG,IAAI,CAAC;IACpC;EACF;AACF;AAEA,UAAUI,eAAeA,CAACJ,IAAkB,EAAkC;EAAA,IAAAK,eAAA;EAC5E,MAAMC,MAAM,GAAG,OAAO,IAAAC,aAAc,EAACP,IAAI,CAAC;EAE1C,QAAAK,eAAA,GAAOC,MAAM,oBAANA,MAAM,CAAEE,OAAO,YAAAH,eAAA,GAAI,IAAI;AAChC;AACA,MAAMI,iBAAiB,GAAGlB,SAAMA,CAAC,CAACa,eAAe,CAAC;AAC3C,SAASM,gBAAgBA,CAC9B,GAAGhB,IAAgD,EACnD;EACA,OAAO,IAAAC,uCAAoB,EAACc,iBAAiB,CAACb,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC/D;AACO,SAASiB,eAAeA,CAC7B,GAAGjB,IAA+C,EAClD;EACA,OAAO,IAAAC,uCAAoB,EAACc,iBAAiB,CAACX,IAAI,CAAC,CAAC,GAAGJ,IAAI,CAAC;AAC9D;AACO,SAASkB,WAAWA,CACzBZ,IAA2C,EAC3CC,QAA2D,EAC3D;EACA,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IAC1B,IAAAP,uCAAoB,EAACc,iBAAiB,CAACN,OAAO,CAAC,CAACH,IAAI,EAAEC,QAAQ,CAAC;EACjE,CAAC,MAAM,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IACrC,IAAAL,uCAAoB,EAACc,iBAAiB,CAACN,OAAO,CAAC,CAC7CD,SAAS,EACTF,IACF,CAAC;EACH,CAAC,MAAM;IAKE;MACL,OAAOW,eAAe,CAACX,IAAI,CAAC;IAC9B;EACF;AACF;AAEA,MAAMa,sBAAsB,GAAGtB,SAAMA,CAAC,CAACuB,sBAAoB,CAAC;AACrD,SAASC,qBAAqBA,CACnC,GAAGrB,IAAqD,EACxD;EACA,OAAO,IAAAC,uCAAoB,EAACkB,sBAAsB,CAACjB,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AACpE;AACO,SAASsB,oBAAoBA,CAClC,GAAGtB,IAAoD,EACvD;EACA,OAAO,IAAAC,uCAAoB,EAACkB,sBAAsB,CAACf,IAAI,CAAC,CAAC,GAAGJ,IAAI,CAAC;AACnE;AACO,SAASuB,gBAAgBA,CAC9BC,MAAoB,EACpBV,OAAmD,EACnDP,QAAkE,EAClE;EACA,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IAC1B,IAAAP,uCAAoB,EAACkB,sBAAsB,CAACV,OAAO,CAAC,CAClDe,MAAM,EACNV,OAAO,EACPP,QACF,CAAC;EACH,CAAC,MAAM,IAAI,OAAOO,OAAO,KAAK,UAAU,EAAE;IACxC,IAAAb,uCAAoB,EAACkB,sBAAsB,CAACV,OAAO,CAAC,CAClDe,MAAM,EACNhB,SAAS,EACTD,QACF,CAAC;EACH,CAAC,MAAM;IAKE;MACL,OAAOe,oBAAoB,CAACE,MAAM,EAAEV,OAAO,CAAC;IAC9C;EACF;AACF;AAAC", "ignoreList": []}
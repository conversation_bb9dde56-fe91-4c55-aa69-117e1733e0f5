import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import ErrorBoundary from './components/common/ErrorBoundary';
import ScrollToTop from './components/common/ScrollToTop';
import LoadingSpinner from './components/common/LoadingSpinner';
import ProtectedRoute from './components/common/ProtectedRoute';
import { AuthProvider, USER_ROLES } from './contexts/AuthContext';
import { ToastProvider } from './components/common/Toast';

// Import pages directly for debugging
import HomePage from './pages/public/HomePage';
import ProductsPage from './pages/public/ProductsPage';
import AboutPage from './pages/public/AboutPage';
import ContactPage from './pages/public/ContactPage';
import LoginPage from './pages/public/LoginPage';
import RegisterPage from './pages/public/RegisterPage';
import HelpCenter from './pages/public/HelpCenter';
import NotFoundPage from './pages/public/NotFoundPage';
import TestPage from './TestPage';
import TestDashboard from './TestDashboard';

// Dashboard pages
import SellerDashboard from './pages/seller/SellerDashboard';
import ProductCreator from './pages/seller/ProductCreator';
import AdvancedProductCreator from './pages/seller/AdvancedProductCreator';
import MyProducts from './pages/seller/MyProducts';
import StoreIntegrations from './pages/seller/StoreIntegrations';
import OrderManagement from './pages/seller/OrderManagement';
import Analytics from './pages/seller/Analytics';
import Inventory from './pages/seller/Inventory';
import Support from './pages/seller/Support';
import Settings from './pages/seller/Settings';
import ManufacturerDashboard from './pages/manufacturer/ManufacturerDashboard';
import OrderQueue from './pages/manufacturer/OrderQueue';
import ProductCatalog from './pages/manufacturer/ProductCatalog';
import AffiliateDashboard from './pages/affiliate/AffiliateDashboard';
import AdminDashboard from './pages/admin/AdminDashboard';

// Loading fallback component
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <LoadingSpinner size="xl" />
      <p className="mt-4 text-gray-600 text-lg">Loading...</p>
    </div>
  </div>
);

function App() {
  return (
    <ToastProvider>
      <AuthProvider>
        <ErrorBoundary>
          <Routes>
            {/* Public routes with header/footer */}
            <Route path="/" element={
              <div className="flex flex-col min-h-screen">
                <Header />
                <main className="flex-grow">
                  <HomePage />
                </main>
                <Footer />
                <ScrollToTop />
              </div>
            } />
            <Route path="/products" element={
              <div className="flex flex-col min-h-screen">
                <Header />
                <main className="flex-grow">
                  <ProductsPage />
                </main>
                <Footer />
                <ScrollToTop />
              </div>
            } />
            <Route path="/about" element={
              <div className="flex flex-col min-h-screen">
                <Header />
                <main className="flex-grow">
                  <AboutPage />
                </main>
                <Footer />
                <ScrollToTop />
              </div>
            } />
            <Route path="/contact" element={
              <div className="flex flex-col min-h-screen">
                <Header />
                <main className="flex-grow">
                  <ContactPage />
                </main>
                <Footer />
                <ScrollToTop />
              </div>
            } />
            <Route path="/help" element={<HelpCenter />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/test" element={<TestPage />} />
            <Route path="/test-dashboard" element={<TestDashboard />} />

            {/* Protected seller routes */}
            <Route path="/seller/dashboard" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <SellerDashboard />
              </ProtectedRoute>
            } />
            <Route path="/seller/products/create" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <ProductCreator />
              </ProtectedRoute>
            } />
            <Route path="/seller/products/advanced-create" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <AdvancedProductCreator />
              </ProtectedRoute>
            } />
            <Route path="/seller/products" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <MyProducts />
              </ProtectedRoute>
            } />
            <Route path="/seller/integrations" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <StoreIntegrations />
              </ProtectedRoute>
            } />
            <Route path="/seller/orders" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <OrderManagement />
              </ProtectedRoute>
            } />
            <Route path="/seller/analytics" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <Analytics />
              </ProtectedRoute>
            } />
            <Route path="/seller/inventory" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <Inventory />
              </ProtectedRoute>
            } />
            <Route path="/seller/support" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <Support />
              </ProtectedRoute>
            } />
            <Route path="/seller/settings" element={
              <ProtectedRoute requiredRole={USER_ROLES.SELLER}>
                <Settings />
              </ProtectedRoute>
            } />

            {/* Protected manufacturer routes */}
            <Route path="/manufacturer/dashboard" element={
              <ProtectedRoute requiredRole={USER_ROLES.MANUFACTURER}>
                <ManufacturerDashboard />
              </ProtectedRoute>
            } />
            <Route path="/manufacturer/orders" element={
              <ProtectedRoute requiredRole={USER_ROLES.MANUFACTURER}>
                <OrderQueue />
              </ProtectedRoute>
            } />
            <Route path="/manufacturer/catalog" element={
              <ProtectedRoute requiredRole={USER_ROLES.MANUFACTURER}>
                <ProductCatalog />
              </ProtectedRoute>
            } />

            {/* Protected affiliate routes */}
            <Route path="/affiliate/dashboard" element={
              <ProtectedRoute requiredRole={USER_ROLES.AFFILIATE}>
                <AffiliateDashboard />
              </ProtectedRoute>
            } />

            {/* Protected admin routes */}
            <Route path="/admin/dashboard" element={
              <ProtectedRoute requiredRole={USER_ROLES.ADMIN}>
                <AdminDashboard />
              </ProtectedRoute>
            } />

            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </ErrorBoundary>
      </AuthProvider>
    </ToastProvider>
  );
}

export default App;
{"name": "drophing-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "preview": "vite preview", "clean": "rm -rf dist node_modules/.vite", "reinstall": "rm -rf node_modules package-lock.json && npm install", "check": "npm run lint && npm run build", "start": "npm run dev"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@paypal/react-paypal-js": "^8.8.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-router-dom": "^6.23.1", "recharts": "^3.1.0", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "^3.6.2", "tailwindcss": "^3.4.3", "vite": "^5.2.0"}}